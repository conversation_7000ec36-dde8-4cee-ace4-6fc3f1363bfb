# Insight Panel

A transparent, floating notification aggregation panel for Windows 11 that provides a unified view of notifications from multiple sources including WhatsApp, Instagram, LinkedIn, and Email.

## Features

### Core Features
- **Transparent Panel**: Fully transparent with blur effect, becomes opaque on hover or new notifications
- **Always-on-Top**: Pin feature to keep panel above all other windows
- **Real-Time Notifications**: Instant updates from multiple sources
- **Actionable Notifications**: Mark as read, quick replies, open source apps
- **Customizable**: Adjust size, position, transparency, and enabled sources
- **Data Backup**: Local SQLite storage with JSON backup functionality

### Supported Sources
- **Windows System Notifications**: All system-level notifications
- **WhatsApp**: Desktop notifications (via UserNotificationListener)
- **Instagram**: DMs, story replies, likes, comments (API integration planned)
- **LinkedIn**: Connection requests, job alerts, InMails (API integration planned)
- **Email**: Outlook, Gmail, and other providers (API integration planned)

## Architecture

### Project Structure
```
InsightPanel/
├── src/
│   ├── InsightPanel/              # Main WPF application
│   │   ├── Views/                 # XAML views and windows
│   │   ├── ViewModels/            # MVVM view models
│   │   ├── Controls/              # Custom user controls
│   │   ├── Converters/            # Value converters for data binding
│   │   ├── Styles/                # XAML styles and themes
│   │   └── Utilities/             # Windows API helpers
│   └── InsightPanel.Core/         # Core business logic
│       ├── Models/                # Data models
│       ├── Services/              # Business services
│       ├── Interfaces/            # Service contracts
│       └── Data/                  # Data access layer
├── tests/
│   └── InsightPanel.Tests/        # Unit and integration tests
└── README.md
```

### Key Components

#### 1. Window Management
- **MainWindow**: Transparent WPF window with blur effects
- **WindowsApiHelper**: Native Windows API integration for transparency and always-on-top
- **DwmExtendFrameIntoClientArea**: Windows composition for blur effects

#### 2. Notification System
- **NotificationAggregatorService**: Central hub for all notification sources
- **WindowsNotificationService**: UserNotificationListener API integration
- **EmailService**: Microsoft Graph API and Gmail API integration (planned)
- **SocialMediaService**: Instagram and LinkedIn API integration (planned)

#### 3. Data Layer
- **SqliteDataService**: Local SQLite database for notification storage
- **SettingsService**: User preferences and panel configuration
- **JSON Backup**: Export/import functionality for data portability

#### 4. UI Components
- **NotificationCard**: Individual notification display component
- **MVVM Pattern**: Clean separation of concerns with data binding
- **Modern UI**: Windows 11-style design with animations

## Setup Instructions

### Prerequisites
- Windows 11 (required for UserNotificationListener API)
- .NET 8.0 SDK
- Visual Studio 2022 or Visual Studio Code
- SQLite (included via NuGet)

### Installation

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd InsightPanel
   ```

2. **Restore Dependencies**
   ```bash
   dotnet restore
   ```

3. **Build the Solution**
   ```bash
   dotnet build
   ```

4. **Run the Application**
   ```bash
   dotnet run --project src/InsightPanel
   ```

### Configuration

#### First Run
1. The application will request notification access permissions
2. Grant access when prompted by Windows
3. Configure notification sources in the settings panel
4. Adjust panel position and transparency as needed

#### API Configuration (Future)
For full functionality with external services, you'll need to configure:

1. **Microsoft Graph API** (for Outlook/Office 365)
   - Register app in Azure AD
   - Configure OAuth 2.0 permissions
   - Add client ID/secret to app settings

2. **Gmail API** (for Gmail)
   - Enable Gmail API in Google Cloud Console
   - Configure OAuth 2.0 credentials
   - Add credentials to app settings

3. **Instagram Graph API** (for Instagram)
   - Register app with Meta for Developers
   - Configure Instagram Basic Display API
   - Add app credentials to settings

4. **LinkedIn API** (for LinkedIn)
   - Register app in LinkedIn Developer Portal
   - Configure OAuth 2.0 permissions
   - Add credentials to app settings

## Usage

### Basic Operations
- **Pin/Unpin**: Click the pin icon in the header to toggle always-on-top
- **Move Panel**: Drag the header to reposition the panel
- **Resize**: Use the resize grip in the bottom-right corner
- **Settings**: Click the gear icon to open settings

### Notification Actions
- **Mark as Read**: Click the "Mark as Read" button on any notification
- **Quick Reply**: Use predefined messages for fast responses
- **Open App**: Click "Open App" to launch the source application
- **Archive/Delete**: Manage notifications with archive and delete options

### Customization
- **Transparency**: Adjust opacity in settings (10% - 100%)
- **Auto-Hide**: Enable to reduce opacity when no new notifications
- **Sources**: Enable/disable specific notification sources
- **Quick Replies**: Customize predefined response messages
- **Position**: Choose from preset positions or set custom location

## Development

### Building from Source
```bash
# Debug build
dotnet build --configuration Debug

# Release build
dotnet build --configuration Release
```

### Running Tests
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Technical Details

### Windows API Integration
- **UserNotificationListener**: System-level notification access
- **DWM Composition**: Blur and transparency effects
- **Window Management**: Always-on-top and positioning
- **Process Integration**: Launch external applications

### Performance Considerations
- **Efficient Polling**: Optimized API polling intervals
- **Memory Management**: Proper disposal of resources
- **Database Optimization**: Indexed SQLite queries
- **UI Virtualization**: Smooth scrolling for large notification lists

### Security
- **OAuth 2.0**: Secure authentication for external APIs
- **Local Storage**: Encrypted sensitive data storage
- **Permission Model**: Minimal required permissions
- **Data Privacy**: Local-first approach with optional cloud sync

## Troubleshooting

### Common Issues
1. **Notification Access Denied**: Re-run as administrator and grant permissions
2. **Blur Effect Not Working**: Ensure Windows 11 with latest updates
3. **API Rate Limits**: Check API quotas and implement proper throttling
4. **Performance Issues**: Reduce polling frequency or notification history

### Logs and Debugging
- Application logs: `%AppData%\InsightPanel\logs\`
- Database location: `%AppData%\InsightPanel\notifications.db`
- Settings file: `%AppData%\InsightPanel\settings.json`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Windows App SDK team for UserNotificationListener API
- ModernWpfUI for Windows 11-style controls
- Community contributors and testers
