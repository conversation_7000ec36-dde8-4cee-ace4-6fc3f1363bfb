using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace InsightPanel.Core.Models;

public class NotificationItem : INotifyPropertyChanged
{
    private bool _isRead;
    private bool _isHighlighted;

    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public NotificationSource SourceType { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public string? ImageUrl { get; set; }
    public string? ActionUrl { get; set; }
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    public Dictionary<string, object> Metadata { get; set; } = new();

    public bool IsRead
    {
        get => _isRead;
        set
        {
            if (_isRead != value)
            {
                _isRead = value;
                OnPropertyChanged();
            }
        }
    }

    public bool IsHighlighted
    {
        get => _isHighlighted;
        set
        {
            if (_isHighlighted != value)
            {
                _isHighlighted = value;
                OnPropertyChanged();
            }
        }
    }

    public List<QuickAction> QuickActions { get; set; } = new();

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public enum NotificationSource
{
    WhatsApp,
    Instagram,
    LinkedIn,
    Email,
    System,
    Other
}

public enum NotificationPriority
{
    Low,
    Normal,
    High,
    Urgent
}

public class QuickAction
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Label { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public QuickActionType Type { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public enum QuickActionType
{
    MarkAsRead,
    QuickReply,
    OpenApp,
    Archive,
    Delete,
    Custom
}
