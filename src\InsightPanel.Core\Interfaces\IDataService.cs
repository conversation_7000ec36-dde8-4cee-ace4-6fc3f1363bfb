using InsightPanel.Core.Models;

namespace InsightPanel.Core.Interfaces;

public interface IDataService
{
    Task InitializeAsync();
    Task<IEnumerable<NotificationItem>> GetNotificationsAsync(int limit = 100);
    Task<NotificationItem?> GetNotificationAsync(string id);
    Task SaveNotificationAsync(NotificationItem notification);
    Task UpdateNotificationAsync(NotificationItem notification);
    Task DeleteNotificationAsync(string id);
    Task<PanelSettings> GetSettingsAsync();
    Task SaveSettingsAsync(PanelSettings settings);
    Task BackupDataAsync(string filePath);
    Task RestoreDataAsync(string filePath);
    Task CleanupOldNotificationsAsync(TimeSpan maxAge);
}

public interface ISettingsService
{
    PanelSettings Settings { get; }
    event EventHandler<SettingsChangedEventArgs>? SettingsChanged;
    
    Task LoadSettingsAsync();
    Task SaveSettingsAsync();
    void UpdateSetting<T>(string key, T value);
    T GetSetting<T>(string key, T defaultValue);
}

public class SettingsChangedEventArgs : EventArgs
{
    public string PropertyName { get; }
    public object? OldValue { get; }
    public object? NewValue { get; }

    public SettingsChangedEventArgs(string propertyName, object? oldValue, object? newValue)
    {
        PropertyName = propertyName;
        OldValue = oldValue;
        NewValue = newValue;
    }
}
