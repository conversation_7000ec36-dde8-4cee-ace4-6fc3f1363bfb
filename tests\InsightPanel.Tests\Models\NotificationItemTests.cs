using Xunit;
using FluentAssertions;
using InsightPanel.Core.Models;
using System.ComponentModel;

namespace InsightPanel.Tests.Models;

public class NotificationItemTests
{
    [Fact]
    public void NotificationItem_ShouldInitializeWithDefaults()
    {
        // Act
        var notification = new NotificationItem();

        // Assert
        notification.Id.Should().NotBeNullOrEmpty();
        notification.Title.Should().Be(string.Empty);
        notification.Content.Should().Be(string.Empty);
        notification.Source.Should().Be(string.Empty);
        notification.SourceType.Should().Be(NotificationSource.WhatsApp);
        notification.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        notification.Priority.Should().Be(NotificationPriority.Normal);
        notification.IsRead.Should().BeFalse();
        notification.IsHighlighted.Should().BeFalse();
        notification.Metadata.Should().NotBeNull();
        notification.QuickActions.Should().NotBeNull();
    }

    [Fact]
    public void IsRead_WhenChanged_ShouldRaisePropertyChanged()
    {
        // Arrange
        var notification = new NotificationItem();
        var propertyChangedRaised = false;
        string? changedPropertyName = null;

        notification.PropertyChanged += (sender, args) =>
        {
            propertyChangedRaised = true;
            changedPropertyName = args.PropertyName;
        };

        // Act
        notification.IsRead = true;

        // Assert
        propertyChangedRaised.Should().BeTrue();
        changedPropertyName.Should().Be(nameof(NotificationItem.IsRead));
    }

    [Fact]
    public void IsHighlighted_WhenChanged_ShouldRaisePropertyChanged()
    {
        // Arrange
        var notification = new NotificationItem();
        var propertyChangedRaised = false;
        string? changedPropertyName = null;

        notification.PropertyChanged += (sender, args) =>
        {
            propertyChangedRaised = true;
            changedPropertyName = args.PropertyName;
        };

        // Act
        notification.IsHighlighted = true;

        // Assert
        propertyChangedRaised.Should().BeTrue();
        changedPropertyName.Should().Be(nameof(NotificationItem.IsHighlighted));
    }

    [Fact]
    public void IsRead_WhenSetToSameValue_ShouldNotRaisePropertyChanged()
    {
        // Arrange
        var notification = new NotificationItem { IsRead = false };
        var propertyChangedRaised = false;

        notification.PropertyChanged += (sender, args) => propertyChangedRaised = true;

        // Act
        notification.IsRead = false;

        // Assert
        propertyChangedRaised.Should().BeFalse();
    }

    [Theory]
    [InlineData(NotificationSource.WhatsApp)]
    [InlineData(NotificationSource.Instagram)]
    [InlineData(NotificationSource.LinkedIn)]
    [InlineData(NotificationSource.Email)]
    [InlineData(NotificationSource.System)]
    [InlineData(NotificationSource.Other)]
    public void SourceType_ShouldAcceptAllValidValues(NotificationSource sourceType)
    {
        // Arrange & Act
        var notification = new NotificationItem { SourceType = sourceType };

        // Assert
        notification.SourceType.Should().Be(sourceType);
    }

    [Theory]
    [InlineData(NotificationPriority.Low)]
    [InlineData(NotificationPriority.Normal)]
    [InlineData(NotificationPriority.High)]
    [InlineData(NotificationPriority.Urgent)]
    public void Priority_ShouldAcceptAllValidValues(NotificationPriority priority)
    {
        // Arrange & Act
        var notification = new NotificationItem { Priority = priority };

        // Assert
        notification.Priority.Should().Be(priority);
    }

    [Fact]
    public void QuickActions_ShouldBeModifiable()
    {
        // Arrange
        var notification = new NotificationItem();
        var quickAction = new QuickAction
        {
            Label = "Test Action",
            Action = "test_action",
            Type = QuickActionType.MarkAsRead
        };

        // Act
        notification.QuickActions.Add(quickAction);

        // Assert
        notification.QuickActions.Should().HaveCount(1);
        notification.QuickActions[0].Label.Should().Be("Test Action");
    }

    [Fact]
    public void Metadata_ShouldBeModifiable()
    {
        // Arrange
        var notification = new NotificationItem();

        // Act
        notification.Metadata["test_key"] = "test_value";

        // Assert
        notification.Metadata.Should().ContainKey("test_key");
        notification.Metadata["test_key"].Should().Be("test_value");
    }
}
