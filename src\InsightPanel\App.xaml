<Application x:Class="InsightPanel.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ui:ThemeResources />
                <ui:XamlControlsResources />
                <ResourceDictionary Source="Styles/NotificationStyles.xaml" />
                <ResourceDictionary Source="Styles/PanelStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Global Colors -->
            <SolidColorBrush x:Key="PanelBackgroundBrush" Color="#CC000000" />
            <SolidColorBrush x:Key="PanelHoverBackgroundBrush" Color="#DD000000" />
            <SolidColorBrush x:Key="NotificationBackgroundBrush" Color="#EE1E1E1E" />
            <SolidColorBrush x:Key="NotificationHoverBackgroundBrush" Color="#FF2D2D2D" />
            <SolidColorBrush x:Key="AccentBrush" Color="#FF0078D4" />
            <SolidColorBrush x:Key="TextPrimaryBrush" Color="#FFFFFFFF" />
            <SolidColorBrush x:Key="TextSecondaryBrush" Color="#FFCCCCCC" />
            
            <!-- Animations -->
            <Storyboard x:Key="FadeInAnimation">
                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                               From="0" To="1" Duration="0:0:0.3">
                    <DoubleAnimation.EasingFunction>
                        <QuadraticEase EasingMode="EaseOut" />
                    </DoubleAnimation.EasingFunction>
                </DoubleAnimation>
            </Storyboard>
            
            <Storyboard x:Key="SlideInFromRightAnimation">
                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                               From="300" To="0" Duration="0:0:0.4">
                    <DoubleAnimation.EasingFunction>
                        <BackEase EasingMode="EaseOut" Amplitude="0.3" />
                    </DoubleAnimation.EasingFunction>
                </DoubleAnimation>
            </Storyboard>
        </ResourceDictionary>
    </Application.Resources>
</Application>
