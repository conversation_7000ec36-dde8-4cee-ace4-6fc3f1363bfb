using System.Collections.Concurrent;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Models;

namespace InsightPanel.Core.Services;

public class NotificationAggregatorService : INotificationService
{
    private readonly IWindowsNotificationService _windowsNotificationService;
    private readonly IEmailService _emailService;
    private readonly ISocialMediaService _socialMediaService;
    private readonly ISettingsService _settingsService;
    private readonly ConcurrentDictionary<string, NotificationItem> _notifications = new();
    
    private bool _isStarted = false;

    public event EventHandler<NotificationReceivedEventArgs>? NotificationReceived;
    public event EventHandler<NotificationUpdatedEventArgs>? NotificationUpdated;

    public NotificationAggregatorService(
        IWindowsNotificationService windowsNotificationService,
        IEmailService emailService,
        ISocialMediaService socialMediaService,
        ISettingsService settingsService)
    {
        _windowsNotificationService = windowsNotificationService;
        _emailService = emailService;
        _socialMediaService = socialMediaService;
        _settingsService = settingsService;
    }

    public async Task StartAsync()
    {
        if (_isStarted) return;

        try
        {
            // Subscribe to all notification sources
            _windowsNotificationService.NotificationReceived += OnWindowsNotificationReceived;
            _emailService.NotificationReceived += OnEmailNotificationReceived;
            _socialMediaService.NotificationReceived += OnSocialMediaNotificationReceived;

            // Start all services
            await _windowsNotificationService.StartListeningAsync();
            
            if (IsSourceEnabled(NotificationSource.Email))
            {
                await _emailService.StartAsync();
            }
            
            if (IsSourceEnabled(NotificationSource.Instagram) || IsSourceEnabled(NotificationSource.LinkedIn))
            {
                await _socialMediaService.StartAsync();
            }

            _isStarted = true;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to start notification aggregator: {ex.Message}", ex);
        }
    }

    public async Task StopAsync()
    {
        if (!_isStarted) return;

        try
        {
            // Unsubscribe from events
            _windowsNotificationService.NotificationReceived -= OnWindowsNotificationReceived;
            _emailService.NotificationReceived -= OnEmailNotificationReceived;
            _socialMediaService.NotificationReceived -= OnSocialMediaNotificationReceived;

            // Stop all services
            await _windowsNotificationService.StopListeningAsync();
            await _emailService.StopAsync();
            await _socialMediaService.StopAsync();

            _notifications.Clear();
            _isStarted = false;
        }
        catch (Exception)
        {
            // Ignore errors during shutdown
        }
    }

    public async Task<IEnumerable<NotificationItem>> GetNotificationsAsync()
    {
        var allNotifications = new List<NotificationItem>();

        // Get from Windows notifications
        var windowsNotifications = await _windowsNotificationService.GetExistingNotificationsAsync();
        allNotifications.AddRange(windowsNotifications.Where(n => IsSourceEnabled(n.SourceType)));

        // Get from email service
        if (IsSourceEnabled(NotificationSource.Email))
        {
            var emailNotifications = await _emailService.GetNotificationsAsync();
            allNotifications.AddRange(emailNotifications);
        }

        // Get from social media service
        if (IsSourceEnabled(NotificationSource.Instagram) || IsSourceEnabled(NotificationSource.LinkedIn))
        {
            var socialNotifications = await _socialMediaService.GetNotificationsAsync();
            allNotifications.AddRange(socialNotifications.Where(n => IsSourceEnabled(n.SourceType)));
        }

        return allNotifications.OrderByDescending(n => n.Timestamp);
    }

    public async Task MarkAsReadAsync(string notificationId)
    {
        if (!_notifications.TryGetValue(notificationId, out var notification))
            return;

        notification.IsRead = true;
        notification.IsHighlighted = false;

        // Delegate to appropriate service
        switch (notification.SourceType)
        {
            case NotificationSource.Email:
                await _emailService.MarkAsReadAsync(notificationId);
                break;
            case NotificationSource.Instagram:
            case NotificationSource.LinkedIn:
                await _socialMediaService.MarkAsReadAsync(notificationId);
                break;
            default:
                // For Windows notifications, just update local state
                break;
        }

        NotificationUpdated?.Invoke(this, new NotificationUpdatedEventArgs(notification, "marked_read"));
    }

    public async Task ArchiveNotificationAsync(string notificationId)
    {
        if (!_notifications.TryGetValue(notificationId, out var notification))
            return;

        // Delegate to appropriate service
        switch (notification.SourceType)
        {
            case NotificationSource.Email:
                await _emailService.ArchiveNotificationAsync(notificationId);
                break;
            default:
                // For other types, just remove from local collection
                _notifications.TryRemove(notificationId, out _);
                break;
        }

        NotificationUpdated?.Invoke(this, new NotificationUpdatedEventArgs(notification, "archived"));
    }

    public async Task DeleteNotificationAsync(string notificationId)
    {
        if (!_notifications.TryGetValue(notificationId, out var notification))
            return;

        // Delegate to appropriate service
        switch (notification.SourceType)
        {
            case NotificationSource.Email:
                await _emailService.DeleteNotificationAsync(notificationId);
                break;
            case NotificationSource.Instagram:
            case NotificationSource.LinkedIn:
                await _socialMediaService.DeleteNotificationAsync(notificationId);
                break;
            default:
                // For Windows notifications, just remove from local collection
                break;
        }

        _notifications.TryRemove(notificationId, out _);
        NotificationUpdated?.Invoke(this, new NotificationUpdatedEventArgs(notification, "deleted"));
    }

    public async Task SendQuickReplyAsync(string notificationId, string message)
    {
        if (!_notifications.TryGetValue(notificationId, out var notification))
            return;

        // Delegate to appropriate service
        switch (notification.SourceType)
        {
            case NotificationSource.WhatsApp:
                // TODO: Implement WhatsApp quick reply
                throw new NotImplementedException("WhatsApp quick reply not implemented yet");
            case NotificationSource.Email:
                await _emailService.SendQuickReplyAsync(notificationId, message);
                break;
            case NotificationSource.Instagram:
            case NotificationSource.LinkedIn:
                await _socialMediaService.SendQuickReplyAsync(notificationId, message);
                break;
            default:
                throw new NotSupportedException($"Quick reply not supported for {notification.SourceType}");
        }

        NotificationUpdated?.Invoke(this, new NotificationUpdatedEventArgs(notification, "replied"));
    }

    public async Task OpenSourceAppAsync(string notificationId)
    {
        if (!_notifications.TryGetValue(notificationId, out var notification))
            return;

        // Get app path or URL from metadata
        var actionUrl = notification.ActionUrl ?? GetDefaultAppUrl(notification.SourceType);
        
        if (!string.IsNullOrEmpty(actionUrl))
        {
            try
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = actionUrl,
                    UseShellExecute = true
                });
            }
            catch (Exception)
            {
                // Fallback to opening the main app
                var appPath = GetAppPath(notification.SourceType);
                if (!string.IsNullOrEmpty(appPath))
                {
                    System.Diagnostics.Process.Start(appPath);
                }
            }
        }

        await Task.CompletedTask;
    }

    public bool IsSourceEnabled(NotificationSource source)
    {
        return _settingsService.Settings.EnabledSources.GetValueOrDefault(source, true);
    }

    public void SetSourceEnabled(NotificationSource source, bool enabled)
    {
        _settingsService.Settings.EnabledSources[source] = enabled;
        _ = Task.Run(async () => await _settingsService.SaveSettingsAsync());
    }

    private void OnWindowsNotificationReceived(object? sender, NotificationReceivedEventArgs e)
    {
        if (!IsSourceEnabled(e.Notification.SourceType))
            return;

        _notifications.TryAdd(e.Notification.Id, e.Notification);
        NotificationReceived?.Invoke(this, e);
    }

    private void OnEmailNotificationReceived(object? sender, NotificationReceivedEventArgs e)
    {
        if (!IsSourceEnabled(NotificationSource.Email))
            return;

        _notifications.TryAdd(e.Notification.Id, e.Notification);
        NotificationReceived?.Invoke(this, e);
    }

    private void OnSocialMediaNotificationReceived(object? sender, NotificationReceivedEventArgs e)
    {
        if (!IsSourceEnabled(e.Notification.SourceType))
            return;

        _notifications.TryAdd(e.Notification.Id, e.Notification);
        NotificationReceived?.Invoke(this, e);
    }

    private string? GetDefaultAppUrl(NotificationSource sourceType)
    {
        return sourceType switch
        {
            NotificationSource.WhatsApp => "whatsapp://",
            NotificationSource.Instagram => "instagram://",
            NotificationSource.LinkedIn => "linkedin://",
            NotificationSource.Email => "mailto:",
            _ => null
        };
    }

    private string? GetAppPath(NotificationSource sourceType)
    {
        return sourceType switch
        {
            NotificationSource.WhatsApp => @"C:\Users\<USER>\AppData\Local\WhatsApp\WhatsApp.exe",
            NotificationSource.Email => "outlook.exe",
            _ => null
        };
    }
}
