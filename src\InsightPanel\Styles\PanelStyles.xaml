<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Panel Window Style -->
    <Style x:Key="PanelWindowStyle" TargetType="Window">
        <Setter Property="WindowStyle" Value="None" />
        <Setter Property="AllowsTransparency" Value="True" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="ShowInTaskbar" Value="False" />
        <Setter Property="ResizeMode" Value="CanResizeWithGrip" />
    </Style>

    <!-- Panel Border Style -->
    <Style x:Key="PanelBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PanelBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="10" />
        <Setter Property="BorderBrush" Value="#33FFFFFF" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Margin" Value="5" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PanelHoverBackgroundBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Header Style -->
    <Style x:Key="HeaderStyle" TargetType="Grid">
        <Setter Property="Background" Value="#22FFFFFF" />
        <Setter Property="Height" Value="40" />
    </Style>

    <!-- Header Button Style -->
    <Style x:Key="HeaderButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="30" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#22FFFFFF" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#44FFFFFF" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Header Title Style -->
    <Style x:Key="HeaderTitleStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Margin" Value="15,0,0,0" />
    </Style>

    <!-- Status Bar Style -->
    <Style x:Key="StatusBarStyle" TargetType="Grid">
        <Setter Property="Background" Value="#22FFFFFF" />
        <Setter Property="Height" Value="30" />
    </Style>

    <!-- Status Text Style -->
    <Style x:Key="StatusTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
        <Setter Property="FontSize" Value="11" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Center" />
    </Style>

    <!-- Scroll Viewer Style -->
    <Style x:Key="NotificationScrollViewerStyle" TargetType="ScrollViewer">
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="Margin" Value="5" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        
                        <ScrollContentPresenter Grid.Column="0" />
                        
                        <ScrollBar Grid.Column="1"
                                  Name="PART_VerticalScrollBar"
                                  Orientation="Vertical"
                                  Value="{TemplateBinding VerticalOffset}"
                                  Maximum="{TemplateBinding ScrollableHeight}"
                                  ViewportSize="{TemplateBinding ViewportHeight}"
                                  Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                  Width="8"
                                  Margin="2,0,0,0"
                                  Style="{StaticResource CustomScrollBarStyle}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Custom ScrollBar Style -->
    <Style x:Key="CustomScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid>
                        <Border Background="{TemplateBinding Background}" CornerRadius="4" />
                        <Track Name="PART_Track" IsDirectionReversed="True">
                            <Track.Thumb>
                                <Thumb Style="{StaticResource ScrollBarThumbStyle}" />
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ScrollBar Thumb Style -->
    <Style x:Key="ScrollBarThumbStyle" TargetType="Thumb">
        <Setter Property="Background" Value="#44FFFFFF" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Border Background="{TemplateBinding Background}" CornerRadius="4" />
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#66FFFFFF" />
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Setter Property="Background" Value="#88FFFFFF" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Resize Grip Style -->
    <Style x:Key="ResizeGripStyle" TargetType="ResizeGrip">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="#44FFFFFF" />
        <Setter Property="HorizontalAlignment" Value="Right" />
        <Setter Property="VerticalAlignment" Value="Bottom" />
        <Setter Property="Margin" Value="0,0,5,5" />
    </Style>

</ResourceDictionary>
