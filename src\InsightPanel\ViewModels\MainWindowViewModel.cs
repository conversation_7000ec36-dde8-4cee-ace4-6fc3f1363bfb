using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Models;

namespace InsightPanel.ViewModels;

public partial class MainWindowViewModel : ObservableObject
{
    private readonly INotificationService _notificationService;
    private readonly ISettingsService _settingsService;
    private readonly IDataService _dataService;
    
    [ObservableProperty]
    private ObservableCollection<NotificationItem> _notifications = new();
    
    [ObservableProperty]
    private string _statusText = "Ready";
    
    [ObservableProperty]
    private double _currentOpacity = 0.9;
    
    [ObservableProperty]
    private bool _hasNewNotifications = false;

    public PanelSettings Settings => _settingsService.Settings;

    public ICommand TogglePinCommand { get; }
    public ICommand ShowSettingsCommand { get; }
    public ICommand CloseCommand { get; }
    public ICommand MarkAsReadCommand { get; }
    public ICommand QuickReplyCommand { get; }
    public ICommand OpenSourceAppCommand { get; }

    public MainWindowViewModel(
        INotificationService notificationService,
        ISettingsService settingsService,
        IDataService dataService)
    {
        _notificationService = notificationService;
        _settingsService = settingsService;
        _dataService = dataService;

        // Initialize commands
        TogglePinCommand = new RelayCommand(TogglePin);
        ShowSettingsCommand = new RelayCommand(ShowSettings);
        CloseCommand = new RelayCommand(Close);
        MarkAsReadCommand = new RelayCommand<string>(MarkAsRead);
        QuickReplyCommand = new RelayCommand<(string, string)>(QuickReply);
        OpenSourceAppCommand = new RelayCommand<string>(OpenSourceApp);

        // Subscribe to events
        _notificationService.NotificationReceived += OnNotificationReceived;
        _notificationService.NotificationUpdated += OnNotificationUpdated;
        _settingsService.SettingsChanged += OnSettingsChanged;

        // Initialize
        _ = InitializeAsync();
    }

    private async Task InitializeAsync()
    {
        try
        {
            StatusText = "Initializing...";
            
            // Start notification service
            await _notificationService.StartAsync();
            
            // Load existing notifications
            var existingNotifications = await _dataService.GetNotificationsAsync(50);
            foreach (var notification in existingNotifications.OrderByDescending(n => n.Timestamp))
            {
                Notifications.Add(notification);
            }
            
            StatusText = $"{Notifications.Count} notifications";
        }
        catch (Exception ex)
        {
            StatusText = $"Error: {ex.Message}";
        }
    }

    private void OnNotificationReceived(object? sender, NotificationReceivedEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            // Add to top of list
            Notifications.Insert(0, e.Notification);
            
            // Highlight new notification
            e.Notification.IsHighlighted = true;
            HasNewNotifications = true;
            
            // Auto-remove highlight after 5 seconds
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            timer.Tick += (s, args) =>
            {
                e.Notification.IsHighlighted = false;
                timer.Stop();
                CheckForNewNotifications();
            };
            timer.Start();
            
            // Update status
            StatusText = $"{Notifications.Count} notifications";
            
            // Save to database
            _ = Task.Run(async () => await _dataService.SaveNotificationAsync(e.Notification));
        });
    }

    private void OnNotificationUpdated(object? sender, NotificationUpdatedEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            var existing = Notifications.FirstOrDefault(n => n.Id == e.Notification.Id);
            if (existing != null)
            {
                var index = Notifications.IndexOf(existing);
                Notifications[index] = e.Notification;
            }
            
            // Save to database
            _ = Task.Run(async () => await _dataService.UpdateNotificationAsync(e.Notification));
        });
    }

    private void OnSettingsChanged(object? sender, SettingsChangedEventArgs e)
    {
        OnPropertyChanged(nameof(Settings));
        
        if (e.PropertyName == nameof(PanelSettings.Opacity))
        {
            CurrentOpacity = Settings.Opacity;
        }
    }

    private void CheckForNewNotifications()
    {
        HasNewNotifications = Notifications.Any(n => n.IsHighlighted);
    }

    private void TogglePin()
    {
        Settings.IsPinned = !Settings.IsPinned;
        _ = Task.Run(async () => await _settingsService.SaveSettingsAsync());
    }

    private void ShowSettings()
    {
        // TODO: Implement settings window
        StatusText = "Settings window not implemented yet";
    }

    private void Close()
    {
        App.Current.Shutdown();
    }

    private async void MarkAsRead(string? notificationId)
    {
        if (string.IsNullOrEmpty(notificationId)) return;
        
        try
        {
            await _notificationService.MarkAsReadAsync(notificationId);
            StatusText = "Marked as read";
        }
        catch (Exception ex)
        {
            StatusText = $"Error: {ex.Message}";
        }
    }

    private async void QuickReply((string notificationId, string message) parameters)
    {
        try
        {
            await _notificationService.SendQuickReplyAsync(parameters.notificationId, parameters.message);
            StatusText = "Reply sent";
        }
        catch (Exception ex)
        {
            StatusText = $"Error: {ex.Message}";
        }
    }

    private async void OpenSourceApp(string? notificationId)
    {
        if (string.IsNullOrEmpty(notificationId)) return;
        
        try
        {
            await _notificationService.OpenSourceAppAsync(notificationId);
            StatusText = "Opened source app";
        }
        catch (Exception ex)
        {
            StatusText = $"Error: {ex.Message}";
        }
    }
}
