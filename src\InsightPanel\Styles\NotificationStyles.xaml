<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:InsightPanel.Converters">

    <!-- Converters -->
    <converters:SourceTypeToColorConverter x:Key="SourceTypeToColorConverter" />
    <converters:RelativeTimeConverter x:Key="RelativeTimeConverter" />
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
    <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
    <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />

    <!-- Notification Card Styles -->
    <Style x:Key="NotificationCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource NotificationBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="Padding" Value="12" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource NotificationHoverBackgroundBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Notification Title Style -->
    <Style x:Key="NotificationTitleStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="FontSize" Value="13" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="MaxHeight" Value="40" />
        <Setter Property="TextTrimming" Value="CharacterEllipsis" />
    </Style>

    <!-- Notification Content Style -->
    <Style x:Key="NotificationContentStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
        <Setter Property="FontSize" Value="11" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="MaxHeight" Value="60" />
        <Setter Property="TextTrimming" Value="CharacterEllipsis" />
    </Style>

    <!-- Notification Source Style -->
    <Style x:Key="NotificationSourceStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
        <Setter Property="FontSize" Value="11" />
        <Setter Property="FontWeight" Value="Medium" />
    </Style>

    <!-- Notification Timestamp Style -->
    <Style x:Key="NotificationTimestampStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}" />
        <Setter Property="FontSize" Value="10" />
    </Style>

    <!-- Quick Action Button Style -->
    <Style x:Key="QuickActionButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="#33FFFFFF" />
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="FontSize" Value="10" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#22FFFFFF" />
                            <Setter Property="BorderBrush" Value="#66FFFFFF" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#44FFFFFF" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Source Icon Style -->
    <Style x:Key="SourceIconStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="16" />
        <Setter Property="Height" Value="16" />
        <Setter Property="Margin" Value="0,0,8,0" />
    </Style>

    <!-- Highlight Animation -->
    <Storyboard x:Key="HighlightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         From="0.95" To="1.0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3" />
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         From="0.95" To="1.0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3" />
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Fade In Animation -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <QuadraticEase EasingMode="EaseOut" />
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
