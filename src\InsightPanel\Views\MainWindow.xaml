<Window x:Class="InsightPanel.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.modernwpf.com/2019"
        xmlns:controls="clr-namespace:InsightPanel.Controls"
        mc:Ignorable="d"
        Title="Insight Panel"
        Width="300" Height="800"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ShowInTaskbar="False"
        Topmost="{Binding Settings.IsPinned}"
        Left="{Binding Settings.Left, Mode=TwoWay}"
        Top="{Binding Settings.Top, Mode=TwoWay}"
        Opacity="{Binding CurrentOpacity}"
        ResizeMode="CanResizeWithGrip">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </Window.Resources>

    <Border x:Name="MainBorder"
            Background="{StaticResource PanelBackgroundBrush}"
            CornerRadius="10"
            BorderBrush="#33FFFFFF"
            BorderThickness="1"
            Margin="5">
        
        <Border.Effect>
            <BlurEffect Radius="20" />
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" 
                  Background="#22FFFFFF"
                  Height="40"
                  MouseLeftButtonDown="Header_MouseLeftButtonDown">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="Insight Panel"
                          Foreground="{StaticResource TextPrimaryBrush}"
                          FontSize="14"
                          FontWeight="SemiBold"
                          VerticalAlignment="Center"
                          Margin="15,0,0,0" />

                <!-- Pin Button -->
                <Button Grid.Column="1"
                        x:Name="PinButton"
                        Width="30" Height="30"
                        Background="Transparent"
                        BorderThickness="0"
                        Command="{Binding TogglePinCommand}"
                        ToolTip="Pin to top">
                    <Path Data="M12,2A2,2 0 0,1 14,4V8H16L14,10V16H10V10L8,8H10V4A2,2 0 0,1 12,2Z"
                          Fill="{Binding Settings.IsPinned, Converter={StaticResource BooleanToColorConverter}}"
                          Width="16" Height="16" />
                </Button>

                <!-- Settings Button -->
                <Button Grid.Column="2"
                        Width="30" Height="30"
                        Background="Transparent"
                        BorderThickness="0"
                        Command="{Binding ShowSettingsCommand}"
                        ToolTip="Settings">
                    <Path Data="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"
                          Fill="{StaticResource TextPrimaryBrush}"
                          Width="16" Height="16" />
                </Button>

                <!-- Close Button -->
                <Button Grid.Column="3"
                        Width="30" Height="30"
                        Background="Transparent"
                        BorderThickness="0"
                        Command="{Binding CloseCommand}"
                        ToolTip="Close">
                    <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                          Fill="{StaticResource TextPrimaryBrush}"
                          Width="16" Height="16" />
                </Button>
            </Grid>

            <!-- Notifications List -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         Margin="5">
                <ItemsControl ItemsSource="{Binding Notifications}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <controls:NotificationCard Margin="0,2" />
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>

            <!-- Status Bar -->
            <Grid Grid.Row="2"
                  Background="#22FFFFFF"
                  Height="30">
                <TextBlock Text="{Binding StatusText}"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          FontSize="11"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center" />
            </Grid>
        </Grid>
    </Border>
</Window>
