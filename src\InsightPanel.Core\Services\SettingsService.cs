using System.ComponentModel;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Models;

namespace InsightPanel.Core.Services;

public class SettingsService : ISettingsService
{
    private readonly IDataService _dataService;
    private PanelSettings _settings;

    public PanelSettings Settings => _settings;

    public event EventHandler<SettingsChangedEventArgs>? SettingsChanged;

    public SettingsService(IDataService dataService)
    {
        _dataService = dataService;
        _settings = new PanelSettings();
        _settings.PropertyChanged += OnSettingsPropertyChanged;
    }

    public async Task LoadSettingsAsync()
    {
        try
        {
            var loadedSettings = await _dataService.GetSettingsAsync();
            
            // Unsubscribe temporarily to avoid triggering events during load
            _settings.PropertyChanged -= OnSettingsPropertyChanged;
            
            // Copy properties from loaded settings
            _settings.Width = loadedSettings.Width;
            _settings.Height = loadedSettings.Height;
            _settings.Left = loadedSettings.Left;
            _settings.Top = loadedSettings.Top;
            _settings.Opacity = loadedSettings.Opacity;
            _settings.IsPinned = loadedSettings.IsPinned;
            _settings.AutoHide = loadedSettings.AutoHide;
            _settings.Position = loadedSettings.Position;
            _settings.EnabledSources = new Dictionary<NotificationSource, bool>(loadedSettings.EnabledSources);
            _settings.QuickReplyMessages = new List<string>(loadedSettings.QuickReplyMessages);
            
            // Re-subscribe to property changes
            _settings.PropertyChanged += OnSettingsPropertyChanged;
        }
        catch (Exception)
        {
            // If loading fails, use default settings
            _settings = new PanelSettings();
            _settings.PropertyChanged += OnSettingsPropertyChanged;
        }
    }

    public async Task SaveSettingsAsync()
    {
        try
        {
            await _dataService.SaveSettingsAsync(_settings);
        }
        catch (Exception)
        {
            // Ignore save errors for now
            // TODO: Add proper error handling and user notification
        }
    }

    public void UpdateSetting<T>(string key, T value)
    {
        var property = typeof(PanelSettings).GetProperty(key);
        if (property != null && property.CanWrite)
        {
            var oldValue = property.GetValue(_settings);
            property.SetValue(_settings, value);
            
            SettingsChanged?.Invoke(this, new SettingsChangedEventArgs(key, oldValue, value));
        }
    }

    public T GetSetting<T>(string key, T defaultValue)
    {
        var property = typeof(PanelSettings).GetProperty(key);
        if (property != null && property.CanRead)
        {
            var value = property.GetValue(_settings);
            if (value is T typedValue)
            {
                return typedValue;
            }
        }
        
        return defaultValue;
    }

    private void OnSettingsPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName != null)
        {
            var property = typeof(PanelSettings).GetProperty(e.PropertyName);
            var newValue = property?.GetValue(_settings);
            
            SettingsChanged?.Invoke(this, new SettingsChangedEventArgs(e.PropertyName, null, newValue));
            
            // Auto-save settings when they change
            _ = Task.Run(SaveSettingsAsync);
        }
    }
}
