using Xunit;
using Moq;
using FluentAssertions;
using InsightPanel.Core.Services;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Models;

namespace InsightPanel.Tests.Services;

public class NotificationAggregatorServiceTests
{
    private readonly Mock<IWindowsNotificationService> _mockWindowsService;
    private readonly Mock<IEmailService> _mockEmailService;
    private readonly Mock<ISocialMediaService> _mockSocialMediaService;
    private readonly Mock<ISettingsService> _mockSettingsService;
    private readonly NotificationAggregatorService _service;

    public NotificationAggregatorServiceTests()
    {
        _mockWindowsService = new Mock<IWindowsNotificationService>();
        _mockEmailService = new Mock<IEmailService>();
        _mockSocialMediaService = new Mock<ISocialMediaService>();
        _mockSettingsService = new Mock<ISettingsService>();

        var settings = new PanelSettings();
        _mockSettingsService.Setup(x => x.Settings).Returns(settings);

        _service = new NotificationAggregatorService(
            _mockWindowsService.Object,
            _mockEmailService.Object,
            _mockSocialMediaService.Object,
            _mockSettingsService.Object);
    }

    [Fact]
    public async Task StartAsync_ShouldStartAllServices()
    {
        // Act
        await _service.StartAsync();

        // Assert
        _mockWindowsService.Verify(x => x.StartListeningAsync(), Times.Once);
        _mockEmailService.Verify(x => x.StartAsync(), Times.Once);
        _mockSocialMediaService.Verify(x => x.StartAsync(), Times.Once);
    }

    [Fact]
    public async Task StopAsync_ShouldStopAllServices()
    {
        // Arrange
        await _service.StartAsync();

        // Act
        await _service.StopAsync();

        // Assert
        _mockWindowsService.Verify(x => x.StopListeningAsync(), Times.Once);
        _mockEmailService.Verify(x => x.StopAsync(), Times.Once);
        _mockSocialMediaService.Verify(x => x.StopAsync(), Times.Once);
    }

    [Fact]
    public async Task MarkAsReadAsync_ShouldDelegateToCorrectService()
    {
        // Arrange
        var notification = new NotificationItem
        {
            Id = "test-id",
            SourceType = NotificationSource.Email
        };

        await _service.StartAsync();

        // Simulate notification being tracked
        _mockEmailService.Raise(x => x.NotificationReceived += null, 
            new NotificationReceivedEventArgs(notification));

        // Act
        await _service.MarkAsReadAsync("test-id");

        // Assert
        _mockEmailService.Verify(x => x.MarkAsReadAsync("test-id"), Times.Once);
    }

    [Fact]
    public void IsSourceEnabled_ShouldReturnCorrectValue()
    {
        // Arrange
        var settings = new PanelSettings();
        settings.EnabledSources[NotificationSource.WhatsApp] = false;
        _mockSettingsService.Setup(x => x.Settings).Returns(settings);

        // Act
        var result = _service.IsSourceEnabled(NotificationSource.WhatsApp);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void SetSourceEnabled_ShouldUpdateSettings()
    {
        // Arrange
        var settings = new PanelSettings();
        _mockSettingsService.Setup(x => x.Settings).Returns(settings);

        // Act
        _service.SetSourceEnabled(NotificationSource.Instagram, false);

        // Assert
        settings.EnabledSources[NotificationSource.Instagram].Should().BeFalse();
        _mockSettingsService.Verify(x => x.SaveSettingsAsync(), Times.Once);
    }

    [Fact]
    public async Task NotificationReceived_ShouldForwardEvent()
    {
        // Arrange
        var notification = new NotificationItem
        {
            Id = "test-id",
            SourceType = NotificationSource.WhatsApp
        };

        NotificationReceivedEventArgs? receivedArgs = null;
        _service.NotificationReceived += (sender, args) => receivedArgs = args;

        await _service.StartAsync();

        // Act
        _mockWindowsService.Raise(x => x.NotificationReceived += null, 
            new NotificationReceivedEventArgs(notification));

        // Assert
        receivedArgs.Should().NotBeNull();
        receivedArgs!.Notification.Id.Should().Be("test-id");
    }
}
