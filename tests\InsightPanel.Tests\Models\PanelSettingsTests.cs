using Xunit;
using FluentAssertions;
using InsightPanel.Core.Models;

namespace InsightPanel.Tests.Models;

public class PanelSettingsTests
{
    [Fact]
    public void PanelSettings_ShouldInitializeWithDefaults()
    {
        // Act
        var settings = new PanelSettings();

        // Assert
        settings.Width.Should().Be(300);
        settings.Height.Should().Be(800);
        settings.Left.Should().Be(100);
        settings.Top.Should().Be(100);
        settings.Opacity.Should().Be(0.9);
        settings.IsPinned.Should().BeFalse();
        settings.AutoHide.Should().BeTrue();
        settings.Position.Should().Be(PanelPosition.Right);
        settings.EnabledSources.Should().NotBeNull();
        settings.QuickReplyMessages.Should().NotBeNull();
    }

    [Fact]
    public void EnabledSources_ShouldHaveDefaultValues()
    {
        // Act
        var settings = new PanelSettings();

        // Assert
        settings.EnabledSources[NotificationSource.WhatsApp].Should().BeTrue();
        settings.EnabledSources[NotificationSource.Instagram].Should().BeTrue();
        settings.EnabledSources[NotificationSource.LinkedIn].Should().BeTrue();
        settings.EnabledSources[NotificationSource.Email].Should().BeTrue();
        settings.EnabledSources[NotificationSource.System].Should().BeTrue();
    }

    [Fact]
    public void QuickReplyMessages_ShouldHaveDefaultValues()
    {
        // Act
        var settings = new PanelSettings();

        // Assert
        settings.QuickReplyMessages.Should().Contain("Thanks!");
        settings.QuickReplyMessages.Should().Contain("Got it.");
        settings.QuickReplyMessages.Should().Contain("Will check later.");
        settings.QuickReplyMessages.Should().Contain("On my way!");
        settings.QuickReplyMessages.Should().Contain("Sure thing!");
    }

    [Theory]
    [InlineData(0.1, 0.1)]
    [InlineData(0.5, 0.5)]
    [InlineData(1.0, 1.0)]
    [InlineData(1.5, 1.0)] // Should clamp to 1.0
    [InlineData(0.05, 0.1)] // Should clamp to 0.1
    public void Opacity_ShouldClampToValidRange(double input, double expected)
    {
        // Arrange
        var settings = new PanelSettings();

        // Act
        settings.Opacity = input;

        // Assert
        settings.Opacity.Should().Be(expected);
    }

    [Fact]
    public void Width_WhenChanged_ShouldRaisePropertyChanged()
    {
        // Arrange
        var settings = new PanelSettings();
        var propertyChangedRaised = false;
        string? changedPropertyName = null;

        settings.PropertyChanged += (sender, args) =>
        {
            propertyChangedRaised = true;
            changedPropertyName = args.PropertyName;
        };

        // Act
        settings.Width = 400;

        // Assert
        propertyChangedRaised.Should().BeTrue();
        changedPropertyName.Should().Be(nameof(PanelSettings.Width));
    }

    [Fact]
    public void IsPinned_WhenChanged_ShouldRaisePropertyChanged()
    {
        // Arrange
        var settings = new PanelSettings();
        var propertyChangedRaised = false;
        string? changedPropertyName = null;

        settings.PropertyChanged += (sender, args) =>
        {
            propertyChangedRaised = true;
            changedPropertyName = args.PropertyName;
        };

        // Act
        settings.IsPinned = true;

        // Assert
        propertyChangedRaised.Should().BeTrue();
        changedPropertyName.Should().Be(nameof(PanelSettings.IsPinned));
    }

    [Fact]
    public void Width_WhenSetToSameValue_ShouldNotRaisePropertyChanged()
    {
        // Arrange
        var settings = new PanelSettings { Width = 300 };
        var propertyChangedRaised = false;

        settings.PropertyChanged += (sender, args) => propertyChangedRaised = true;

        // Act
        settings.Width = 300;

        // Assert
        propertyChangedRaised.Should().BeFalse();
    }

    [Theory]
    [InlineData(PanelPosition.Left)]
    [InlineData(PanelPosition.Right)]
    [InlineData(PanelPosition.Top)]
    [InlineData(PanelPosition.Bottom)]
    [InlineData(PanelPosition.Custom)]
    public void Position_ShouldAcceptAllValidValues(PanelPosition position)
    {
        // Arrange & Act
        var settings = new PanelSettings { Position = position };

        // Assert
        settings.Position.Should().Be(position);
    }

    [Fact]
    public void EnabledSources_ShouldBeModifiable()
    {
        // Arrange
        var settings = new PanelSettings();

        // Act
        settings.EnabledSources[NotificationSource.WhatsApp] = false;

        // Assert
        settings.EnabledSources[NotificationSource.WhatsApp].Should().BeFalse();
    }

    [Fact]
    public void QuickReplyMessages_ShouldBeModifiable()
    {
        // Arrange
        var settings = new PanelSettings();

        // Act
        settings.QuickReplyMessages.Add("Custom message");

        // Assert
        settings.QuickReplyMessages.Should().Contain("Custom message");
    }
}
