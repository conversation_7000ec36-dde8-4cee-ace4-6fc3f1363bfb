using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Services;
using InsightPanel.ViewModels;
using InsightPanel.Views;

namespace InsightPanel;

public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // Configure services
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Core services
                services.AddSingleton<IDataService, SqliteDataService>();
                services.AddSingleton<ISettingsService, SettingsService>();
                services.AddSingleton<INotificationService, NotificationAggregatorService>();
                
                // Platform-specific services
                services.AddSingleton<IWindowsNotificationService, WindowsNotificationService>();
                services.AddSingleton<IEmailService, EmailService>();
                services.AddSingleton<ISocialMediaService, SocialMediaService>();
                
                // ViewModels
                services.AddTransient<MainWindowViewModel>();
                services.AddTransient<SettingsViewModel>();
                
                // Views
                services.AddTransient<MainWindow>();
                services.AddTransient<SettingsWindow>();
            })
            .Build();

        await _host.StartAsync();

        // Initialize services
        var dataService = _host.Services.GetRequiredService<IDataService>();
        await dataService.InitializeAsync();

        var settingsService = _host.Services.GetRequiredService<ISettingsService>();
        await settingsService.LoadSettingsAsync();

        // Show main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        
        base.OnExit(e);
    }
}
