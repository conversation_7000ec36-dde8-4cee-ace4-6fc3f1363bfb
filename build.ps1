# Insight Panel Build Script
param(
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\publish",
    [switch]$Clean,
    [switch]$Test,
    [switch]$Package
)

Write-Host "Building Insight Panel..." -ForegroundColor Green

# Clean previous builds if requested
if ($Clean) {
    Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
    }
    dotnet clean
}

# Restore dependencies
Write-Host "Restoring dependencies..." -ForegroundColor Yellow
dotnet restore

# Build the solution
Write-Host "Building solution..." -ForegroundColor Yellow
dotnet build --configuration $Configuration --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

# Run tests if requested
if ($Test) {
    Write-Host "Running tests..." -ForegroundColor Yellow
    dotnet test --configuration $Configuration --no-build --verbosity normal
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Tests failed!" -ForegroundColor Red
        exit 1
    }
}

# Publish the application
Write-Host "Publishing application..." -ForegroundColor Yellow
dotnet publish src/InsightPanel/InsightPanel.csproj `
    --configuration $Configuration `
    --output $OutputPath `
    --self-contained true `
    --runtime win-x64 `
    --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "Publish failed!" -ForegroundColor Red
    exit 1
}

# Package if requested
if ($Package) {
    Write-Host "Creating package..." -ForegroundColor Yellow
    $packageName = "InsightPanel-v1.0.0-win-x64.zip"
    
    if (Get-Command Compress-Archive -ErrorAction SilentlyContinue) {
        Compress-Archive -Path "$OutputPath\*" -DestinationPath $packageName -Force
        Write-Host "Package created: $packageName" -ForegroundColor Green
    } else {
        Write-Host "Compress-Archive not available. Skipping packaging." -ForegroundColor Yellow
    }
}

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Output location: $OutputPath" -ForegroundColor Cyan

# Display next steps
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Run the application: $OutputPath\InsightPanel.exe" -ForegroundColor White
Write-Host "2. Grant notification permissions when prompted" -ForegroundColor White
Write-Host "3. Configure notification sources in settings" -ForegroundColor White
