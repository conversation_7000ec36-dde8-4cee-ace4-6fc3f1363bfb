using System.Data.SQLite;
using System.Text.Json;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Models;

namespace InsightPanel.Core.Services;

public class SqliteDataService : IDataService
{
    private readonly string _connectionString;
    private readonly string _databasePath;

    public SqliteDataService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "InsightPanel");
        Directory.CreateDirectory(appDataPath);
        
        _databasePath = Path.Combine(appDataPath, "notifications.db");
        _connectionString = $"Data Source={_databasePath};Version=3;";
    }

    public async Task InitializeAsync()
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        // Create notifications table
        var createNotificationsTable = @"
            CREATE TABLE IF NOT EXISTS Notifications (
                Id TEXT PRIMARY KEY,
                Title TEXT NOT NULL,
                Content TEXT,
                Source TEXT,
                SourceType INTEGER,
                Timestamp DATETIME,
                ImageUrl TEXT,
                ActionUrl TEXT,
                Priority INTEGER,
                IsRead BOOLEAN DEFAULT 0,
                IsHighlighted BOOLEAN DEFAULT 0,
                Metadata TEXT,
                QuickActions TEXT,
                CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )";

        using var command = new SQLiteCommand(createNotificationsTable, connection);
        await command.ExecuteNonQueryAsync();

        // Create settings table
        var createSettingsTable = @"
            CREATE TABLE IF NOT EXISTS Settings (
                Key TEXT PRIMARY KEY,
                Value TEXT,
                UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )";

        using var settingsCommand = new SQLiteCommand(createSettingsTable, connection);
        await settingsCommand.ExecuteNonQueryAsync();

        // Create indexes for better performance
        var createIndexes = @"
            CREATE INDEX IF NOT EXISTS idx_notifications_timestamp ON Notifications(Timestamp DESC);
            CREATE INDEX IF NOT EXISTS idx_notifications_source ON Notifications(SourceType);
            CREATE INDEX IF NOT EXISTS idx_notifications_read ON Notifications(IsRead);
        ";

        using var indexCommand = new SQLiteCommand(createIndexes, connection);
        await indexCommand.ExecuteNonQueryAsync();
    }

    public async Task<IEnumerable<NotificationItem>> GetNotificationsAsync(int limit = 100)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var query = @"
            SELECT * FROM Notifications 
            ORDER BY Timestamp DESC 
            LIMIT @limit";

        using var command = new SQLiteCommand(query, connection);
        command.Parameters.AddWithValue("@limit", limit);

        using var reader = await command.ExecuteReaderAsync();
        var notifications = new List<NotificationItem>();

        while (await reader.ReadAsync())
        {
            var notification = MapFromDataReader(reader);
            if (notification != null)
            {
                notifications.Add(notification);
            }
        }

        return notifications;
    }

    public async Task<NotificationItem?> GetNotificationAsync(string id)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var query = "SELECT * FROM Notifications WHERE Id = @id";
        using var command = new SQLiteCommand(query, connection);
        command.Parameters.AddWithValue("@id", id);

        using var reader = await command.ExecuteReaderAsync();
        if (await reader.ReadAsync())
        {
            return MapFromDataReader(reader);
        }

        return null;
    }

    public async Task SaveNotificationAsync(NotificationItem notification)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var query = @"
            INSERT OR REPLACE INTO Notifications 
            (Id, Title, Content, Source, SourceType, Timestamp, ImageUrl, ActionUrl, Priority, IsRead, IsHighlighted, Metadata, QuickActions)
            VALUES (@id, @title, @content, @source, @sourceType, @timestamp, @imageUrl, @actionUrl, @priority, @isRead, @isHighlighted, @metadata, @quickActions)";

        using var command = new SQLiteCommand(query, connection);
        command.Parameters.AddWithValue("@id", notification.Id);
        command.Parameters.AddWithValue("@title", notification.Title);
        command.Parameters.AddWithValue("@content", notification.Content);
        command.Parameters.AddWithValue("@source", notification.Source);
        command.Parameters.AddWithValue("@sourceType", (int)notification.SourceType);
        command.Parameters.AddWithValue("@timestamp", notification.Timestamp);
        command.Parameters.AddWithValue("@imageUrl", notification.ImageUrl ?? (object)DBNull.Value);
        command.Parameters.AddWithValue("@actionUrl", notification.ActionUrl ?? (object)DBNull.Value);
        command.Parameters.AddWithValue("@priority", (int)notification.Priority);
        command.Parameters.AddWithValue("@isRead", notification.IsRead);
        command.Parameters.AddWithValue("@isHighlighted", notification.IsHighlighted);
        command.Parameters.AddWithValue("@metadata", JsonSerializer.Serialize(notification.Metadata));
        command.Parameters.AddWithValue("@quickActions", JsonSerializer.Serialize(notification.QuickActions));

        await command.ExecuteNonQueryAsync();
    }

    public async Task UpdateNotificationAsync(NotificationItem notification)
    {
        await SaveNotificationAsync(notification); // SQLite INSERT OR REPLACE handles updates
    }

    public async Task DeleteNotificationAsync(string id)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var query = "DELETE FROM Notifications WHERE Id = @id";
        using var command = new SQLiteCommand(query, connection);
        command.Parameters.AddWithValue("@id", id);

        await command.ExecuteNonQueryAsync();
    }

    public async Task<PanelSettings> GetSettingsAsync()
    {
        var settings = new PanelSettings();

        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var query = "SELECT Key, Value FROM Settings";
        using var command = new SQLiteCommand(query, connection);
        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            var key = reader.GetString("Key");
            var value = reader.GetString("Value");

            try
            {
                switch (key)
                {
                    case nameof(PanelSettings.Width):
                        settings.Width = double.Parse(value);
                        break;
                    case nameof(PanelSettings.Height):
                        settings.Height = double.Parse(value);
                        break;
                    case nameof(PanelSettings.Left):
                        settings.Left = double.Parse(value);
                        break;
                    case nameof(PanelSettings.Top):
                        settings.Top = double.Parse(value);
                        break;
                    case nameof(PanelSettings.Opacity):
                        settings.Opacity = double.Parse(value);
                        break;
                    case nameof(PanelSettings.IsPinned):
                        settings.IsPinned = bool.Parse(value);
                        break;
                    case nameof(PanelSettings.AutoHide):
                        settings.AutoHide = bool.Parse(value);
                        break;
                    case nameof(PanelSettings.Position):
                        settings.Position = Enum.Parse<PanelPosition>(value);
                        break;
                    case nameof(PanelSettings.EnabledSources):
                        var enabledSources = JsonSerializer.Deserialize<Dictionary<NotificationSource, bool>>(value);
                        if (enabledSources != null)
                        {
                            settings.EnabledSources = enabledSources;
                        }
                        break;
                    case nameof(PanelSettings.QuickReplyMessages):
                        var quickReplies = JsonSerializer.Deserialize<List<string>>(value);
                        if (quickReplies != null)
                        {
                            settings.QuickReplyMessages = quickReplies;
                        }
                        break;
                }
            }
            catch (Exception)
            {
                // Ignore invalid settings values
            }
        }

        return settings;
    }

    public async Task SaveSettingsAsync(PanelSettings settings)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var settingsToSave = new Dictionary<string, string>
        {
            [nameof(PanelSettings.Width)] = settings.Width.ToString(),
            [nameof(PanelSettings.Height)] = settings.Height.ToString(),
            [nameof(PanelSettings.Left)] = settings.Left.ToString(),
            [nameof(PanelSettings.Top)] = settings.Top.ToString(),
            [nameof(PanelSettings.Opacity)] = settings.Opacity.ToString(),
            [nameof(PanelSettings.IsPinned)] = settings.IsPinned.ToString(),
            [nameof(PanelSettings.AutoHide)] = settings.AutoHide.ToString(),
            [nameof(PanelSettings.Position)] = settings.Position.ToString(),
            [nameof(PanelSettings.EnabledSources)] = JsonSerializer.Serialize(settings.EnabledSources),
            [nameof(PanelSettings.QuickReplyMessages)] = JsonSerializer.Serialize(settings.QuickReplyMessages)
        };

        foreach (var setting in settingsToSave)
        {
            var query = @"
                INSERT OR REPLACE INTO Settings (Key, Value, UpdatedAt) 
                VALUES (@key, @value, CURRENT_TIMESTAMP)";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@key", setting.Key);
            command.Parameters.AddWithValue("@value", setting.Value);

            await command.ExecuteNonQueryAsync();
        }
    }

    public async Task BackupDataAsync(string filePath)
    {
        var backupData = new
        {
            Notifications = await GetNotificationsAsync(1000),
            Settings = await GetSettingsAsync(),
            BackupDate = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(backupData, new JsonSerializerOptions { WriteIndented = true });
        await File.WriteAllTextAsync(filePath, json);
    }

    public async Task RestoreDataAsync(string filePath)
    {
        var json = await File.ReadAllTextAsync(filePath);
        var backupData = JsonSerializer.Deserialize<JsonElement>(json);

        // Restore notifications
        if (backupData.TryGetProperty("Notifications", out var notificationsElement))
        {
            var notifications = JsonSerializer.Deserialize<List<NotificationItem>>(notificationsElement.GetRawText());
            if (notifications != null)
            {
                foreach (var notification in notifications)
                {
                    await SaveNotificationAsync(notification);
                }
            }
        }

        // Restore settings
        if (backupData.TryGetProperty("Settings", out var settingsElement))
        {
            var settings = JsonSerializer.Deserialize<PanelSettings>(settingsElement.GetRawText());
            if (settings != null)
            {
                await SaveSettingsAsync(settings);
            }
        }
    }

    public async Task CleanupOldNotificationsAsync(TimeSpan maxAge)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();

        var cutoffDate = DateTime.Now.Subtract(maxAge);
        var query = "DELETE FROM Notifications WHERE Timestamp < @cutoffDate";

        using var command = new SQLiteCommand(query, connection);
        command.Parameters.AddWithValue("@cutoffDate", cutoffDate);

        await command.ExecuteNonQueryAsync();
    }

    private NotificationItem? MapFromDataReader(SQLiteDataReader reader)
    {
        try
        {
            var notification = new NotificationItem
            {
                Id = reader.GetString("Id"),
                Title = reader.GetString("Title"),
                Content = reader.IsDBNull("Content") ? string.Empty : reader.GetString("Content"),
                Source = reader.IsDBNull("Source") ? string.Empty : reader.GetString("Source"),
                SourceType = (NotificationSource)reader.GetInt32("SourceType"),
                Timestamp = reader.GetDateTime("Timestamp"),
                ImageUrl = reader.IsDBNull("ImageUrl") ? null : reader.GetString("ImageUrl"),
                ActionUrl = reader.IsDBNull("ActionUrl") ? null : reader.GetString("ActionUrl"),
                Priority = (NotificationPriority)reader.GetInt32("Priority"),
                IsRead = reader.GetBoolean("IsRead"),
                IsHighlighted = reader.GetBoolean("IsHighlighted")
            };

            // Deserialize metadata
            if (!reader.IsDBNull("Metadata"))
            {
                var metadataJson = reader.GetString("Metadata");
                var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(metadataJson);
                if (metadata != null)
                {
                    notification.Metadata = metadata;
                }
            }

            // Deserialize quick actions
            if (!reader.IsDBNull("QuickActions"))
            {
                var quickActionsJson = reader.GetString("QuickActions");
                var quickActions = JsonSerializer.Deserialize<List<QuickAction>>(quickActionsJson);
                if (quickActions != null)
                {
                    notification.QuickActions = quickActions;
                }
            }

            return notification;
        }
        catch (Exception)
        {
            return null;
        }
    }
}
