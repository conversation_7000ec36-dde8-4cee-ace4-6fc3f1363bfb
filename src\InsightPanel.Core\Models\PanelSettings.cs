using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace InsightPanel.Core.Models;

public class PanelSettings : INotifyPropertyChanged
{
    private double _width = 300;
    private double _height = 800;
    private double _left = 100;
    private double _top = 100;
    private double _opacity = 0.9;
    private bool _isPinned = false;
    private bool _autoHide = true;
    private PanelPosition _position = PanelPosition.Right;

    public double Width
    {
        get => _width;
        set
        {
            if (Math.Abs(_width - value) > 0.01)
            {
                _width = value;
                OnPropertyChanged();
            }
        }
    }

    public double Height
    {
        get => _height;
        set
        {
            if (Math.Abs(_height - value) > 0.01)
            {
                _height = value;
                OnPropertyChanged();
            }
        }
    }

    public double Left
    {
        get => _left;
        set
        {
            if (Math.Abs(_left - value) > 0.01)
            {
                _left = value;
                OnPropertyChanged();
            }
        }
    }

    public double Top
    {
        get => _top;
        set
        {
            if (Math.Abs(_top - value) > 0.01)
            {
                _top = value;
                OnPropertyChanged();
            }
        }
    }

    public double Opacity
    {
        get => _opacity;
        set
        {
            if (Math.Abs(_opacity - value) > 0.01)
            {
                _opacity = Math.Max(0.1, Math.Min(1.0, value));
                OnPropertyChanged();
            }
        }
    }

    public bool IsPinned
    {
        get => _isPinned;
        set
        {
            if (_isPinned != value)
            {
                _isPinned = value;
                OnPropertyChanged();
            }
        }
    }

    public bool AutoHide
    {
        get => _autoHide;
        set
        {
            if (_autoHide != value)
            {
                _autoHide = value;
                OnPropertyChanged();
            }
        }
    }

    public PanelPosition Position
    {
        get => _position;
        set
        {
            if (_position != value)
            {
                _position = value;
                OnPropertyChanged();
            }
        }
    }

    public Dictionary<NotificationSource, bool> EnabledSources { get; set; } = new()
    {
        { NotificationSource.WhatsApp, true },
        { NotificationSource.Instagram, true },
        { NotificationSource.LinkedIn, true },
        { NotificationSource.Email, true },
        { NotificationSource.System, true }
    };

    public List<string> QuickReplyMessages { get; set; } = new()
    {
        "Thanks!",
        "Got it.",
        "Will check later.",
        "On my way!",
        "Sure thing!"
    };

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public enum PanelPosition
{
    Left,
    Right,
    Top,
    Bottom,
    Custom
}
