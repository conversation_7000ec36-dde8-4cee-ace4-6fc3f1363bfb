using InsightPanel.Core.Models;

namespace InsightPanel.Core.Interfaces;

public interface INotificationService
{
    event EventHandler<NotificationReceivedEventArgs>? NotificationReceived;
    event EventHandler<NotificationUpdatedEventArgs>? NotificationUpdated;
    
    Task StartAsync();
    Task StopAsync();
    Task<IEnumerable<NotificationItem>> GetNotificationsAsync();
    Task MarkAsReadAsync(string notificationId);
    Task ArchiveNotificationAsync(string notificationId);
    Task DeleteNotificationAsync(string notificationId);
    Task SendQuickReplyAsync(string notificationId, string message);
    Task OpenSourceAppAsync(string notificationId);
    bool IsSourceEnabled(NotificationSource source);
    void SetSourceEnabled(NotificationSource source, bool enabled);
}

public class NotificationReceivedEventArgs : EventArgs
{
    public NotificationItem Notification { get; }

    public NotificationReceivedEventArgs(NotificationItem notification)
    {
        Notification = notification;
    }
}

public class NotificationUpdatedEventArgs : EventArgs
{
    public NotificationItem Notification { get; }
    public string UpdateType { get; }

    public NotificationUpdatedEventArgs(NotificationItem notification, string updateType)
    {
        Notification = notification;
        UpdateType = updateType;
    }
}
