<UserControl x:Class="InsightPanel.Controls.NotificationCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="120" d:DesignWidth="280">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        
        <Style x:Key="QuickActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#33FFFFFF" />
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}" />
            <Setter Property="Padding" Value="8,4" />
            <Setter Property="Margin" Value="2" />
            <Setter Property="FontSize" Value="10" />
            <Setter Property="CornerRadius" Value="3" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#22FFFFFF" />
                                <Setter Property="BorderBrush" Value="#66FFFFFF" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#44FFFFFF" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Border x:Name="CardBorder"
            Background="{StaticResource NotificationBackgroundBrush}"
            CornerRadius="8"
            Margin="2"
            Padding="12"
            BorderThickness="1"
            BorderBrush="Transparent">
        
        <Border.Style>
            <Style TargetType="Border">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}" />
                        <Setter Property="Background" Value="#22007ACC" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsRead}" Value="True">
                        <Setter Property="Opacity" Value="0.7" />
                    </DataTrigger>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource NotificationHoverBackgroundBrush}" />
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Border.Style>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Header with source and timestamp -->
            <Grid Grid.Row="0" Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- Source icon -->
                <Ellipse Grid.Column="0"
                         Width="16" Height="16"
                         Fill="{Binding SourceType, Converter={StaticResource SourceTypeToColorConverter}}"
                         Margin="0,0,8,0" />

                <!-- Source name -->
                <TextBlock Grid.Column="1"
                          Text="{Binding Source}"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          FontSize="11"
                          FontWeight="Medium"
                          VerticalAlignment="Center" />

                <!-- Timestamp -->
                <TextBlock Grid.Column="2"
                          Text="{Binding Timestamp, Converter={StaticResource RelativeTimeConverter}}"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          FontSize="10"
                          VerticalAlignment="Center" />
            </Grid>

            <!-- Title -->
            <TextBlock Grid.Row="1"
                      Text="{Binding Title}"
                      Foreground="{StaticResource TextPrimaryBrush}"
                      FontSize="13"
                      FontWeight="SemiBold"
                      TextWrapping="Wrap"
                      MaxHeight="40"
                      TextTrimming="CharacterEllipsis"
                      Margin="0,0,0,4" />

            <!-- Content -->
            <TextBlock Grid.Row="2"
                      Text="{Binding Content}"
                      Foreground="{StaticResource TextSecondaryBrush}"
                      FontSize="11"
                      TextWrapping="Wrap"
                      MaxHeight="60"
                      TextTrimming="CharacterEllipsis"
                      Margin="0,0,0,8"
                      Visibility="{Binding Content, Converter={StaticResource StringToVisibilityConverter}}" />

            <!-- Quick Actions -->
            <WrapPanel Grid.Row="3"
                      Orientation="Horizontal"
                      Visibility="{Binding QuickActions.Count, Converter={StaticResource CountToVisibilityConverter}}">
                
                <ItemsControl ItemsSource="{Binding QuickActions}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Button Content="{Binding Label}"
                                    Style="{StaticResource QuickActionButtonStyle}"
                                    Command="{Binding DataContext.QuickActionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    CommandParameter="{Binding}" />
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </WrapPanel>
        </Grid>

        <!-- Highlight animation -->
        <Border.Triggers>
            <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           From="0.95" To="1.0" Duration="0:0:0.3">
                                <DoubleAnimation.EasingFunction>
                                    <BackEase EasingMode="EaseOut" Amplitude="0.3" />
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           From="0.95" To="1.0" Duration="0:0:0.3">
                                <DoubleAnimation.EasingFunction>
                                    <BackEase EasingMode="EaseOut" Amplitude="0.3" />
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>
        </Border.Triggers>

        <Border.RenderTransform>
            <ScaleTransform />
        </Border.RenderTransform>
    </Border>
</UserControl>
