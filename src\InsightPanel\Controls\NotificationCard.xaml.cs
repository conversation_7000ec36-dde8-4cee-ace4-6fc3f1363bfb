using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using InsightPanel.Core.Models;

namespace InsightPanel.Controls;

public partial class NotificationCard : UserControl
{
    public static readonly DependencyProperty QuickActionCommandProperty =
        DependencyProperty.Register(nameof(QuickActionCommand), typeof(ICommand), typeof(NotificationCard));

    public ICommand QuickActionCommand
    {
        get => (ICommand)GetValue(QuickActionCommandProperty);
        set => SetValue(QuickActionCommandProperty, value);
    }

    public NotificationCard()
    {
        InitializeComponent();
    }

    private void OnQuickActionClick(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.DataContext is QuickAction action)
        {
            QuickActionCommand?.Execute(action);
        }
    }
}
