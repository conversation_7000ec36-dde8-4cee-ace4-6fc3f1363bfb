using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using InsightPanel.Core.Models;

namespace InsightPanel.Converters;

public class SourceTypeToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is NotificationSource sourceType)
        {
            return sourceType switch
            {
                NotificationSource.WhatsApp => new SolidColorBrush(Color.FromRgb(37, 211, 102)),
                NotificationSource.Instagram => new SolidColorBrush(Color.FromRgb(225, 48, 108)),
                NotificationSource.LinkedIn => new SolidColorBrush(Color.FromRgb(0, 119, 181)),
                NotificationSource.Email => new SolidColorBrush(Color.FromRgb(0, 120, 212)),
                NotificationSource.System => new SolidColorBrush(Color.FromRgb(128, 128, 128)),
                _ => new SolidColorBrush(Color.FromRgb(128, 128, 128))
            };
        }
        
        return new SolidColorBrush(Color.FromRgb(128, 128, 128));
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class RelativeTimeConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;
            
            if (timeSpan.TotalMinutes < 1)
                return "Just now";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}m ago";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}h ago";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}d ago";
            
            return dateTime.ToString("MMM dd");
        }
        
        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class StringToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string str)
        {
            return string.IsNullOrWhiteSpace(str) ? Visibility.Collapsed : Visibility.Visible;
        }
        
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class CountToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int count)
        {
            return count > 0 ? Visibility.Visible : Visibility.Collapsed;
        }
        
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class BooleanToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue 
                ? new SolidColorBrush(Color.FromRgb(0, 120, 212)) // Accent color when true
                : new SolidColorBrush(Color.FromRgb(255, 255, 255)); // White when false
        }
        
        return new SolidColorBrush(Color.FromRgb(255, 255, 255));
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class NotificationPriorityToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is NotificationPriority priority)
        {
            return priority switch
            {
                NotificationPriority.Low => new SolidColorBrush(Color.FromRgb(128, 128, 128)),
                NotificationPriority.Normal => new SolidColorBrush(Color.FromRgb(255, 255, 255)),
                NotificationPriority.High => new SolidColorBrush(Color.FromRgb(255, 193, 7)),
                NotificationPriority.Urgent => new SolidColorBrush(Color.FromRgb(220, 53, 69)),
                _ => new SolidColorBrush(Color.FromRgb(255, 255, 255))
            };
        }
        
        return new SolidColorBrush(Color.FromRgb(255, 255, 255));
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class BooleanToOpacityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? 0.7 : 1.0; // Reduced opacity when true (read), full opacity when false (unread)
        }
        
        return 1.0;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class InverseBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue;
        }
        
        return true;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue;
        }
        
        return false;
    }
}
