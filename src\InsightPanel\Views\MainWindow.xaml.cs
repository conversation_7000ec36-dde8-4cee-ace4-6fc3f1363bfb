using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media.Effects;
using InsightPanel.ViewModels;
using InsightPanel.Utilities;

namespace InsightPanel.Views;

public partial class MainWindow : Window
{
    private readonly MainWindowViewModel _viewModel;
    private bool _isMouseOver = false;
    private readonly System.Windows.Threading.DispatcherTimer _opacityTimer;

    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        DataContext = _viewModel;

        // Initialize opacity timer for auto-hide functionality
        _opacityTimer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(100)
        };
        _opacityTimer.Tick += OpacityTimer_Tick;

        // Set up event handlers
        MouseEnter += MainWindow_MouseEnter;
        MouseLeave += MainWindow_MouseLeave;
        Loaded += MainWindow_Loaded;
        Closing += MainWindow_Closing;

        // Subscribe to settings changes
        _viewModel.Settings.PropertyChanged += Settings_PropertyChanged;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // Enable blur effect and transparency
        EnableBlurEffect();
        
        // Set initial opacity
        UpdateOpacity();
        
        // Start opacity timer
        _opacityTimer.Start();
    }

    private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        _opacityTimer.Stop();
    }

    private void EnableBlurEffect()
    {
        try
        {
            var windowHelper = new WindowInteropHelper(this);
            var accent = new WindowsApiHelper.AccentPolicy
            {
                AccentState = WindowsApiHelper.AccentState.ACCENT_ENABLE_BLURBEHIND,
                GradientColor = 0x00FFFFFF
            };

            var accentStructSize = Marshal.SizeOf(accent);
            var accentPtr = Marshal.AllocHGlobal(accentStructSize);
            Marshal.StructureToPtr(accent, accentPtr, false);

            var data = new WindowsApiHelper.WindowCompositionAttributeData
            {
                Attribute = WindowsApiHelper.WindowCompositionAttribute.WCA_ACCENT_POLICY,
                SizeOfData = accentStructSize,
                Data = accentPtr
            };

            WindowsApiHelper.SetWindowCompositionAttribute(windowHelper.Handle, ref data);
            Marshal.FreeHGlobal(accentPtr);
        }
        catch (Exception ex)
        {
            // Fallback to software blur if Windows API fails
            MainBorder.Effect = new BlurEffect { Radius = 10 };
        }
    }

    private void Settings_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(_viewModel.Settings.IsPinned))
        {
            Topmost = _viewModel.Settings.IsPinned;
        }
        else if (e.PropertyName == nameof(_viewModel.Settings.Opacity))
        {
            UpdateOpacity();
        }
    }

    private void MainWindow_MouseEnter(object sender, MouseEventArgs e)
    {
        _isMouseOver = true;
        UpdateOpacity();
    }

    private void MainWindow_MouseLeave(object sender, MouseEventArgs e)
    {
        _isMouseOver = false;
        UpdateOpacity();
    }

    private void OpacityTimer_Tick(object? sender, EventArgs e)
    {
        // Check if there are new notifications to highlight
        if (_viewModel.HasNewNotifications)
        {
            UpdateOpacity();
        }
    }

    private void UpdateOpacity()
    {
        double targetOpacity;

        if (_viewModel.HasNewNotifications || _isMouseOver)
        {
            // Full opacity when there are new notifications or mouse is over
            targetOpacity = _viewModel.Settings.Opacity;
        }
        else if (_viewModel.Settings.AutoHide)
        {
            // Reduced opacity when auto-hide is enabled
            targetOpacity = _viewModel.Settings.Opacity * 0.3;
        }
        else
        {
            // Normal opacity
            targetOpacity = _viewModel.Settings.Opacity;
        }

        // Animate opacity change
        var animation = new System.Windows.Media.Animation.DoubleAnimation
        {
            To = targetOpacity,
            Duration = TimeSpan.FromMilliseconds(300),
            EasingFunction = new System.Windows.Media.Animation.QuadraticEase
            {
                EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut
            }
        };

        BeginAnimation(OpacityProperty, animation);
    }

    private void Header_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
        {
            DragMove();
        }
    }

    protected override void OnSourceInitialized(EventArgs e)
    {
        base.OnSourceInitialized(e);
        
        // Remove from Alt+Tab
        var windowHelper = new WindowInteropHelper(this);
        WindowsApiHelper.SetWindowLong(windowHelper.Handle, 
            WindowsApiHelper.GWL_EXSTYLE,
            WindowsApiHelper.GetWindowLong(windowHelper.Handle, WindowsApiHelper.GWL_EXSTYLE) | 
            WindowsApiHelper.WS_EX_TOOLWINDOW);
    }
}
