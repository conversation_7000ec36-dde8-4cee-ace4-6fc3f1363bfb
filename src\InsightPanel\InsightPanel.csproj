<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>Assets\icon.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AssemblyTitle>Insight Panel</AssemblyTitle>
    <AssemblyDescription>Transparent notification aggregation panel for Windows</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>Insight Panel</Company>
    <Product>Insight Panel</Product>
    <Copyright>Copyright © 2024</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.WindowsAppSDK" Version="1.4.*********" />
    <PackageReference Include="Microsoft.Windows.SDK.Contracts" Version="10.0.22621.2428" />
    <PackageReference Include="Microsoft.Graph" Version="5.36.0" />
    <PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
    <PackageReference Include="Google.Apis.Gmail.v1" Version="1.68.0.3421" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\InsightPanel.Core\InsightPanel.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Styles\" />
  </ItemGroup>

</Project>
