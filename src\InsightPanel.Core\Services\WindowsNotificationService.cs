using System.Collections.Concurrent;
using Windows.ApplicationModel.UserNotifications;
using Windows.UI.Notifications;
using InsightPanel.Core.Interfaces;
using InsightPanel.Core.Models;

namespace InsightPanel.Core.Services;

public interface IWindowsNotificationService
{
    event EventHandler<NotificationReceivedEventArgs>? NotificationReceived;
    Task StartListeningAsync();
    Task StopListeningAsync();
    Task<IEnumerable<NotificationItem>> GetExistingNotificationsAsync();
}

public class WindowsNotificationService : IWindowsNotificationService
{
    private UserNotificationListener? _listener;
    private readonly ConcurrentDictionary<uint, NotificationItem> _trackedNotifications = new();
    private bool _isListening = false;

    public event EventHandler<NotificationReceivedEventArgs>? NotificationReceived;

    public async Task StartListeningAsync()
    {
        if (_isListening) return;

        try
        {
            // Request access to notifications
            var accessStatus = await UserNotificationListener.RequestAccessAsync();
            if (accessStatus != UserNotificationListenerAccessStatus.Allowed)
            {
                throw new UnauthorizedAccessException("Access to notifications was denied");
            }

            _listener = UserNotificationListener.Current;
            _listener.NotificationChanged += OnNotificationChanged;

            // Load existing notifications
            await LoadExistingNotificationsAsync();

            _isListening = true;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to start Windows notification listener: {ex.Message}", ex);
        }
    }

    public async Task StopListeningAsync()
    {
        if (!_isListening || _listener == null) return;

        _listener.NotificationChanged -= OnNotificationChanged;
        _listener = null;
        _isListening = false;
        _trackedNotifications.Clear();

        await Task.CompletedTask;
    }

    public async Task<IEnumerable<NotificationItem>> GetExistingNotificationsAsync()
    {
        if (_listener == null)
            return Enumerable.Empty<NotificationItem>();

        try
        {
            var notifications = await _listener.GetNotificationsAsync(NotificationKinds.Toast);
            return notifications.Select(ConvertToNotificationItem).Where(n => n != null)!;
        }
        catch (Exception)
        {
            return Enumerable.Empty<NotificationItem>();
        }
    }

    private async Task LoadExistingNotificationsAsync()
    {
        if (_listener == null) return;

        try
        {
            var notifications = await _listener.GetNotificationsAsync(NotificationKinds.Toast);
            foreach (var notification in notifications)
            {
                var item = ConvertToNotificationItem(notification);
                if (item != null)
                {
                    _trackedNotifications.TryAdd(notification.Id, item);
                }
            }
        }
        catch (Exception)
        {
            // Ignore errors when loading existing notifications
        }
    }

    private async void OnNotificationChanged(UserNotificationListener sender, UserNotificationChangedEventArgs args)
    {
        try
        {
            var notification = await GetNotificationByIdAsync(args.UserNotificationId);
            if (notification == null) return;

            var item = ConvertToNotificationItem(notification);
            if (item == null) return;

            switch (args.ChangeKind)
            {
                case UserNotificationChangedKind.Added:
                    _trackedNotifications.TryAdd(notification.Id, item);
                    NotificationReceived?.Invoke(this, new NotificationReceivedEventArgs(item));
                    break;

                case UserNotificationChangedKind.Removed:
                    _trackedNotifications.TryRemove(notification.Id, out _);
                    break;

                case UserNotificationChangedKind.Updated:
                    _trackedNotifications.TryUpdate(notification.Id, item, _trackedNotifications.GetValueOrDefault(notification.Id));
                    break;
            }
        }
        catch (Exception)
        {
            // Ignore individual notification processing errors
        }
    }

    private async Task<UserNotification?> GetNotificationByIdAsync(uint notificationId)
    {
        if (_listener == null) return null;

        try
        {
            var notifications = await _listener.GetNotificationsAsync(NotificationKinds.Toast);
            return notifications.FirstOrDefault(n => n.Id == notificationId);
        }
        catch (Exception)
        {
            return null;
        }
    }

    private NotificationItem? ConvertToNotificationItem(UserNotification notification)
    {
        try
        {
            var binding = notification.Notification.Visual.GetBinding(KnownNotificationBindings.ToastGeneric);
            if (binding == null) return null;

            var title = GetTextFromBinding(binding, "title") ?? "Notification";
            var content = GetTextFromBinding(binding, "text") ?? string.Empty;
            var appDisplayName = notification.AppInfo.DisplayInfo.DisplayName ?? "Unknown App";

            var item = new NotificationItem
            {
                Id = notification.Id.ToString(),
                Title = title,
                Content = content,
                Source = appDisplayName,
                SourceType = DetermineSourceType(appDisplayName),
                Timestamp = notification.CreationTime.DateTime,
                Priority = DeterminePriority(notification),
                Metadata = new Dictionary<string, object>
                {
                    ["OriginalNotification"] = notification,
                    ["AppId"] = notification.AppInfo.Id,
                    ["AppDisplayName"] = appDisplayName
                }
            };

            // Add quick actions based on source type
            AddQuickActions(item);

            return item;
        }
        catch (Exception)
        {
            return null;
        }
    }

    private string? GetTextFromBinding(NotificationBinding binding, string textType)
    {
        try
        {
            return binding.GetTextElements().FirstOrDefault(t => 
                t.Id?.Equals(textType, StringComparison.OrdinalIgnoreCase) == true)?.Text;
        }
        catch (Exception)
        {
            return null;
        }
    }

    private NotificationSource DetermineSourceType(string appName)
    {
        var lowerAppName = appName.ToLowerInvariant();
        
        return lowerAppName switch
        {
            var name when name.Contains("whatsapp") => NotificationSource.WhatsApp,
            var name when name.Contains("instagram") => NotificationSource.Instagram,
            var name when name.Contains("linkedin") => NotificationSource.LinkedIn,
            var name when name.Contains("mail") || name.Contains("outlook") || name.Contains("gmail") => NotificationSource.Email,
            _ => NotificationSource.Other
        };
    }

    private NotificationPriority DeterminePriority(UserNotification notification)
    {
        // Default to normal priority
        // Could be enhanced to check notification content or app-specific rules
        return NotificationPriority.Normal;
    }

    private void AddQuickActions(NotificationItem item)
    {
        // Always add mark as read
        item.QuickActions.Add(new QuickAction
        {
            Label = "Mark as Read",
            Action = "mark_read",
            Type = QuickActionType.MarkAsRead
        });

        // Add source-specific actions
        switch (item.SourceType)
        {
            case NotificationSource.WhatsApp:
                item.QuickActions.Add(new QuickAction
                {
                    Label = "Quick Reply",
                    Action = "quick_reply",
                    Type = QuickActionType.QuickReply
                });
                break;

            case NotificationSource.Email:
                item.QuickActions.Add(new QuickAction
                {
                    Label = "Archive",
                    Action = "archive",
                    Type = QuickActionType.Archive
                });
                break;
        }

        // Always add open app action
        item.QuickActions.Add(new QuickAction
        {
            Label = "Open App",
            Action = "open_app",
            Type = QuickActionType.OpenApp
        });
    }
}
